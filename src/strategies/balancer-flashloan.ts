import { ethers } from 'ethers';
import { FlashloanRoute, ArbitrageRoute, Pool, Token } from '../types';
import { PoolManager } from '../dex/pools';
import { GasOptimizer } from '../gas/optimizer';
import { CalldataEncoder } from '../calldata/encoder';
import { BundleSimulator } from '../simulation/simulator';
import { config, COMMON_TOKENS, ADDRESSES } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';

// Official Balancer V2 Vault interface for flashloans
export const BALANCER_VAULT_ABI = [
  {
    "inputs": [
      {
        "internalType": "contract IFlashLoanRecipient",
        "name": "recipient",
        "type": "address"
      },
      {
        "internalType": "contract IERC20[]",
        "name": "tokens",
        "type": "address[]"
      },
      {
        "internalType": "uint256[]",
        "name": "amounts",
        "type": "uint256[]"
      },
      {
        "internalType": "bytes",
        "name": "userData",
        "type": "bytes"
      }
    ],
    "name": "flashLoan",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// Official Balancer V2 flashloan receiver interface
export const BALANCER_FLASHLOAN_RECEIVER_ABI = [
  {
    "inputs": [
      {
        "internalType": "contract IERC20[]",
        "name": "tokens",
        "type": "address[]"
      },
      {
        "internalType": "uint256[]",
        "name": "amounts",
        "type": "uint256[]"
      },
      {
        "internalType": "uint256[]",
        "name": "feeAmounts",
        "type": "uint256[]"
      },
      {
        "internalType": "bytes",
        "name": "userData",
        "type": "bytes"
      }
    ],
    "name": "receiveFlashLoan",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

export class BalancerFlashloanStrategy {
  protected poolManager: PoolManager;
  protected gasOptimizer: GasOptimizer;
  protected encoder: CalldataEncoder;
  protected simulator: BundleSimulator;
  protected wallet: ethers.Wallet;
  protected provider: ethers.Provider;
  protected balancerVault: ethers.Contract;
  protected balancerContract: ethers.Contract | null = null;
  protected readonly MIN_PROFIT_THRESHOLD: number;
  protected readonly MAX_FLASHLOAN_AMOUNT: bigint;

  constructor(provider: ethers.Provider) {
    this.provider = provider;
    this.poolManager = new PoolManager();
    this.gasOptimizer = new GasOptimizer();
    this.encoder = new CalldataEncoder();
    this.simulator = new BundleSimulator();
    this.wallet = new ethers.Wallet(config.privateKey, provider);
    
    // Network-specific configuration
    const isMainnet = config.chainId === 1;
    this.MIN_PROFIT_THRESHOLD = isMainnet ? 0.003 : 0.015; // Lower threshold due to 0% fees
    this.MAX_FLASHLOAN_AMOUNT = isMainnet 
      ? ethers.parseUnits('50000', 6) // 50k USDC on mainnet (Balancer has less liquidity)
      : ethers.parseUnits('5000', 6);  // 5k USDC on testnet

    // Initialize Balancer Vault contract (same address on all networks)
    const BALANCER_VAULT_ADDRESS = '******************************************';

    this.balancerVault = new ethers.Contract(
      BALANCER_VAULT_ADDRESS,
      BALANCER_VAULT_ABI,
      provider
    );

    // Initialize Balancer contract if address is provided
    if (config.balancerFlashloanContract) {
      this.balancerContract = new ethers.Contract(
        config.balancerFlashloanContract,
        this.getBalancerContractABI(),
        this.wallet
      );
      enhancedLogger.systemStatus(`🔵 Balancer Contract: ${config.balancerFlashloanContract}`);
    } else {
      enhancedLogger.systemStatus('⚠️  No Balancer contract address provided - using vault directly');
    }

    enhancedLogger.systemStatus('🔵 Balancer V2 Flashloan Strategy initialized');
    enhancedLogger.systemStatus(`   💰 ZERO FEES - 100% profit retention!`);
    enhancedLogger.systemStatus(`   🏦 Vault: ${BALANCER_VAULT_ADDRESS}`);
    enhancedLogger.systemStatus(`   📊 Min Profit: ${this.MIN_PROFIT_THRESHOLD * 100}%`);
    enhancedLogger.systemStatus(`   💵 Max Amount: ${ethers.formatUnits(this.MAX_FLASHLOAN_AMOUNT, 6)} USDC`);
  }

  private getBalancerContractABI(): string[] {
    return [
      // Execute flashloan arbitrage
      'function executeFlashloanArbitrage(address[] memory tokens, uint256[] memory amounts, bytes memory userData) external',
      // Withdraw profits
      'function withdrawProfits(address token) external',
      // Emergency withdraw
      'function emergencyWithdraw(address token, uint256 amount) external',
      // Get vault address
      'function getVault() external pure returns (address)'
    ];
  }

  async scanForBalancerFlashloanOpportunities(): Promise<FlashloanRoute[]> {
    const opportunities: FlashloanRoute[] = [];

    try {
      logger.debug('Scanning for Balancer flashloan opportunities...');

      // Focus on USDC flashloans for arbitrage
      const flashloanToken = COMMON_TOKENS.find(t => t.symbol === 'USDC');
      if (!flashloanToken) {
        logger.warn('USDC token not found in COMMON_TOKENS');
        return opportunities;
      }

      // Check Balancer liquidity for the token
      const availableLiquidity = await this.checkBalancerLiquidity(flashloanToken);
      if (availableLiquidity < this.MAX_FLASHLOAN_AMOUNT) {
        logger.debug('Insufficient Balancer liquidity for flashloan', {
          available: ethers.formatUnits(availableLiquidity, 6),
          required: ethers.formatUnits(this.MAX_FLASHLOAN_AMOUNT, 6)
        });
        return opportunities;
      }

      // Scan for arbitrage opportunities between different DEXs
      for (let i = 0; i < COMMON_TOKENS.length; i++) {
        const targetToken = COMMON_TOKENS[i];
        
        if (targetToken.address === flashloanToken.address) {
          continue; // Skip same token
        }

        // Find arbitrage between Uniswap V2 and V3
        const arbitrageRoute = await this.findArbitrageOpportunity(flashloanToken, targetToken);
        
        if (arbitrageRoute) {
          // Calculate optimal flashloan amount
          const flashloanAmount = await this.calculateOptimalFlashloanAmount(
            flashloanToken,
            arbitrageRoute
          );

          if (flashloanAmount > BigInt(0)) {
            const flashloanRoute = await this.buildBalancerFlashloanRoute(
              flashloanToken,
              flashloanAmount,
              arbitrageRoute
            );

            if (flashloanRoute && flashloanRoute.confidence >= 70) {
              opportunities.push(flashloanRoute);
              enhancedLogger.profitCalculation(
                ethers.formatEther(flashloanRoute.expectedProfit),
                true
              );
            }
          }
        }
      }

      // Sort by expected profit
      opportunities.sort((a, b) =>
        Number(BigInt(b.expectedProfit.toString()) - BigInt(a.expectedProfit.toString()))
      );

      // enhancedLogger.systemStatus(`🔵 Found ${opportunities.length} Balancer flashloan opportunities`);
      return opportunities.slice(0, 3); // Return top 3 (Balancer has less liquidity)

    } catch (error) {
      logger.logError(error as Error, 'BalancerFlashloanStrategy.scanForBalancerFlashloanOpportunities');
      return [];
    }
  }

  private async checkBalancerLiquidity(token: Token): Promise<bigint> {
    try {
      // This would require querying Balancer pools for available liquidity
      // For now, return a conservative estimate
      const isMainnet = config.chainId === 1;
      return isMainnet 
        ? ethers.parseUnits('50000', token.decimals) // 50k on mainnet
        : ethers.parseUnits('5000', token.decimals);  // 5k on testnet
    } catch (error) {
      logger.debug('Error checking Balancer liquidity', { error: (error as Error).message });
      return BigInt(0);
    }
  }

  private async findArbitrageOpportunity(
    tokenA: Token,
    tokenB: Token
  ): Promise<ArbitrageRoute | null> {
    try {
      // Get pools from both V2 and V3
      const v2Pool = await this.poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v2');
      const v3Pool = await this.poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v3', 3000);

      if (!v2Pool || !v3Pool) {
        return null;
      }

      // Calculate prices on both pools
      const v2Price = this.calculatePoolPrice(v2Pool, tokenA, tokenB);
      const v3Price = this.calculatePoolPrice(v3Pool, tokenA, tokenB);

      if (!v2Price || !v3Price) {
        return null;
      }

      // Check for price difference (lower threshold due to 0% fees)
      const priceDifference = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);

      if (priceDifference < this.MIN_PROFIT_THRESHOLD) {
        return null;
      }

      // Determine direction (buy low, sell high)
      const buyPool = v2Price < v3Price ? v2Pool : v3Pool;
      const sellPool = v2Price < v3Price ? v3Pool : v2Pool;

      // Calculate optimal amount for arbitrage
      const optimalAmount = await this.calculateOptimalArbitrageAmount(buyPool, sellPool, tokenA, tokenB);

      if (optimalAmount === BigInt(0)) {
        return null;
      }

      // Estimate gas costs
      const gasEstimate = await this.estimateArbitrageGasCost(buyPool, sellPool, optimalAmount);

      // Calculate expected profit (no flashloan fees!)
      const expectedProfit = await this.calculateArbitrageProfit(
        buyPool, sellPool, tokenA, tokenB, optimalAmount, gasEstimate
      );

      if (BigInt(expectedProfit.toString()) <= BigInt(0)) {
        return null;
      }

      return {
        pools: [buyPool, sellPool],
        tokens: [tokenA, tokenB],
        expectedProfit,
        gasEstimate,
        confidence: this.calculateArbitrageConfidence(priceDifference, expectedProfit)
      };

    } catch (error) {
      logger.debug('Error finding arbitrage opportunity', { error: (error as Error).message });
      return null;
    }
  }

  private async buildBalancerFlashloanRoute(
    flashloanToken: Token,
    flashloanAmount: bigint,
    arbitrageRoute: ArbitrageRoute
  ): Promise<FlashloanRoute | null> {
    try {
      // Balancer has ZERO flashloan fees!
      const flashloanPremium = BigInt(0);
      
      // Estimate total gas cost (flashloan + arbitrage)
      const flashloanGas = await this.estimateBalancerFlashloanGasCost();
      const totalGasEstimate = BigInt(arbitrageRoute.gasEstimate.toString()) + flashloanGas;
      
      // Calculate expected profit (no premium to subtract!)
      const arbitrageProfit = await this.estimateArbitrageProfit(arbitrageRoute, flashloanAmount);
      const expectedProfit = arbitrageProfit - totalGasEstimate; // Only gas costs!
      
      if (expectedProfit <= BigInt(0)) {
        return null;
      }

      // Calculate confidence (higher due to no fees)
      const profitMargin = Number(expectedProfit * BigInt(10000) / flashloanAmount) / 100;
      const confidence = this.calculateBalancerFlashloanConfidence(profitMargin, expectedProfit);

      return {
        flashloanToken,
        flashloanAmount,
        flashloanPremium, // Always 0 for Balancer!
        arbitrageRoute,
        expectedProfit,
        gasEstimate: totalGasEstimate,
        confidence
      };

    } catch (error) {
      logger.debug('Error building Balancer flashloan route', { error: (error as Error).message });
      return null;
    }
  }

  private calculateBalancerFlashloanConfidence(profitMargin: number, expectedProfit: bigint): number {
    let confidence = 0;

    // Higher base confidence due to zero fees
    confidence += 20;

    // Profit margin factor (lower threshold due to no fees)
    confidence += Math.min(profitMargin * 15, 40); // Max 40 points

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 20, 30); // Max 30 points

    // Balancer liquidity penalty (less liquidity than Aave)
    confidence -= 10;

    return Math.min(confidence, 100);
  }

  // ... (other helper methods similar to Aave implementation)
  
  private calculatePoolPrice(pool: Pool, token0: Token, token1: Token): number | null {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const reserve0 = Number(ethers.formatUnits(pool.reserves.reserve0, token0.decimals));
      const reserve1 = Number(ethers.formatUnits(pool.reserves.reserve1, token1.decimals));
      return reserve1 / reserve0;
    } else if (pool.protocol === 'uniswap-v3' && pool.sqrtPriceX96) {
      const sqrtPrice = Number(pool.sqrtPriceX96) / (2 ** 96);
      const price = sqrtPrice ** 2;
      const decimalsAdjustment = 10 ** (token1.decimals - token0.decimals);
      return price * decimalsAdjustment;
    }
    return null;
  }

  private async calculateOptimalFlashloanAmount(
    flashloanToken: Token,
    arbitrageRoute: ArbitrageRoute
  ): Promise<bigint> {
    // Similar to Aave implementation but with 0% fees
    const baseAmount = ethers.parseUnits('1000', flashloanToken.decimals);
    const calculatedAmount = baseAmount * BigInt(5);
    return calculatedAmount > this.MAX_FLASHLOAN_AMOUNT ? this.MAX_FLASHLOAN_AMOUNT : calculatedAmount;
  }

  private async estimateBalancerFlashloanGasCost(): Promise<bigint> {
    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return BigInt(180000) * BigInt(gasStrategy.maxFeePerGas.toString()); // Slightly less gas than Aave
  }

  private async calculateOptimalArbitrageAmount(buyPool: Pool, sellPool: Pool, buyToken: Token, sellToken: Token): Promise<bigint> {
    const maxAmount = ethers.parseUnits('5000', buyToken.decimals);
    return maxAmount / BigInt(2);
  }

  private async estimateArbitrageGasCost(buyPool: Pool, sellPool: Pool, amount: bigint): Promise<bigint> {
    const gasPerSwap = BigInt(150000);
    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return gasPerSwap * BigInt(2) * BigInt(gasStrategy.maxFeePerGas.toString());
  }

  private async calculateArbitrageProfit(buyPool: Pool, sellPool: Pool, buyToken: Token, sellToken: Token, amount: bigint, gasCost: bigint): Promise<bigint> {
    const estimatedReturn = (amount * BigInt(102)) / BigInt(100);
    return estimatedReturn - amount - gasCost;
  }

  private calculateArbitrageConfidence(profitPercentage: number, expectedProfit: bigint): number {
    let confidence = 0;
    confidence += Math.min(profitPercentage * 15, 40);
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 20, 25);
    confidence += 15;
    return Math.min(confidence, 100);
  }

  private async estimateArbitrageProfit(arbitrageRoute: ArbitrageRoute, amount: bigint): Promise<bigint> {
    return (amount * BigInt(102)) / BigInt(100) - amount;
  }

  async executeBalancerFlashloan(route: FlashloanRoute): Promise<boolean> {
    try {
      enhancedLogger.separator();
      enhancedLogger.systemStatus('🔵 Executing Balancer Flashloan Arbitrage');
      enhancedLogger.systemStatus(`💰 ZERO FEES - Maximum profit retention!`);
      enhancedLogger.systemStatus(`Flashloan Amount: ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
      enhancedLogger.profitCalculation(ethers.formatEther(route.expectedProfit), true);
      enhancedLogger.systemStatus(`Confidence: ${route.confidence}%`);

      if (config.dryRun) {
        enhancedLogger.systemStatus('DRY RUN: Simulating Balancer flashloan execution...');
        
        enhancedLogger.systemStatus('Step 1: 🔵 Flashloan from Balancer (0% fees!)');
        enhancedLogger.systemStatus(`  └─ Borrowing ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} USDC`);
        enhancedLogger.systemStatus(`  └─ Premium: 0 USDC (FREE!)`);
        
        enhancedLogger.systemStatus('Step 2: 🔄 Execute Arbitrage');
        enhancedLogger.systemStatus(`  └─ Buy on ${route.arbitrageRoute.pools[0].protocol.toUpperCase()}`);
        enhancedLogger.systemStatus(`  └─ Sell on ${route.arbitrageRoute.pools[1].protocol.toUpperCase()}`);
        
        enhancedLogger.systemStatus('Step 3: 💸 Repay Flashloan');
        enhancedLogger.systemStatus(`  └─ Repaying ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} USDC (no premium!)`);
        
        enhancedLogger.systemStatus('Step 4: 💎 Keep 100% of Profit');
        enhancedLogger.profitCalculation(ethers.formatEther(route.expectedProfit), true);
        
        enhancedLogger.success('✅ Balancer flashloan simulation completed successfully');
        enhancedLogger.separator();
        return true;
      }

      // Execute using Balancer contract if available
      if (this.balancerContract) {
        const success = await this.executeWithBalancerContract(route);
        enhancedLogger.success('✅ Balancer flashloan executed via contract');
        return success;
      } else {
        enhancedLogger.systemStatus('⚠️  Balancer flashloan execution requires contract deployment');
        enhancedLogger.systemStatus('   Deploy with: npx hardhat run scripts/deploy-balancer-flashloan.js');
        return false;
      }

    } catch (error) {
      enhancedLogger.error('Balancer flashloan execution failed', error);
      logger.logError(error as Error, 'BalancerFlashloanStrategy.executeBalancerFlashloan');
      return false;
    }
  }

  private async executeWithBalancerContract(route: FlashloanRoute): Promise<boolean> {
    try {
      if (!this.balancerContract) {
        throw new Error('Balancer contract not initialized');
      }

      // Prepare tokens array
      const tokens = [route.flashloanToken.address];
      const amounts = [route.flashloanAmount];

      // Encode arbitrage parameters
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256'],
        [
          route.flashloanToken.address, // tokenA
          route.arbitrageRoute.tokens[1].address, // tokenB
          route.arbitrageRoute.pools[0].protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER, // buyDex
          route.arbitrageRoute.pools[1].protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER, // sellDex
          route.arbitrageRoute.pools[0].fee || 3000, // v3Fee
          route.expectedProfit // minProfit
        ]
      );

      // Execute Balancer flashloan
      const tx = await this.balancerContract.executeFlashloanArbitrage(
        tokens,
        amounts,
        arbitrageParams
      );

      enhancedLogger.systemStatus(`📝 Transaction hash: ${tx.hash}`);

      // Wait for confirmation
      const receipt = await tx.wait();
      enhancedLogger.systemStatus(`✅ Transaction confirmed in block ${receipt.blockNumber}`);

      return true;
    } catch (error) {
      enhancedLogger.error('Balancer contract execution failed', error);
      logger.logError(error as Error, 'BalancerFlashloanStrategy.executeWithBalancerContract');
      return false;
    }
  }
}
