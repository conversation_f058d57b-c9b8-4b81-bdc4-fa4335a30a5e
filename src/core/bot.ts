import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import { FlashbotsBundleProvider } from '@flashbots/ethers-provider-bundle';
import { RPCManager } from '../providers/rpcManager';
import { MempoolMonitor } from '../mempool/monitor';
import { SandwichStrategy } from '../strategies/sandwich';
import { ArbitrageStrategy } from '../strategies/arbitrage';
import { FlashloanStrategy } from '../strategies/flashloan';
import { MEVShareFlashloanStrategy } from '../strategies/mev-share-flashloan';
import { MEVShareEventMonitor } from '../mev-share/event-monitor';
import { FlashbotsBundleManager } from '../flashbots/bundle-provider';
import { AdvancedGasEstimator } from '../gas/advanced-estimator';
import { FlashbotsExecutor } from '../execution/flashbots-executor';
import { BundleSimulator } from '../simulation/simulator';
import { GasOptimizer } from '../gas/optimizer';
import { statusDashboard } from '../utils/statusDashboard';
import { config, validateConfig } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';
import { BotState, MEVOpportunity, Transaction, Bundle, RiskMetrics } from '../types';

export class MEVBot extends EventEmitter {
  private rpcManager: RPCManager;
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashbotsProvider: FlashbotsBundleProvider | null = null;

  private mempoolMonitor: MempoolMonitor;
  private sandwichStrategy: SandwichStrategy;
  private arbitrageStrategy: ArbitrageStrategy;
  private flashloanStrategy: FlashloanStrategy;
  private mevShareFlashloanStrategy: MEVShareFlashloanStrategy | null = null;
  private mevShareMonitor: MEVShareEventMonitor | null = null;
  private flashbotsManager: FlashbotsBundleManager;
  private advancedGasEstimator: AdvancedGasEstimator;
  private flashbotsExecutor: FlashbotsExecutor;
  private simulator: BundleSimulator;
  private gasOptimizer: GasOptimizer;

  private state: BotState;
  private riskMetrics: RiskMetrics;
  private opportunities: MEVOpportunity[] = [];
  private executedBundles: Bundle[] = [];

  private readonly MAX_OPPORTUNITIES = 1000;
  private readonly ARBITRAGE_SCAN_INTERVAL = this.getOptimalScanInterval();
  private arbitrageScanTimer: NodeJS.Timeout | null = null;
  private blockUpdateTimer: NodeJS.Timeout | null = null;

  constructor() {
    super();

    // Initialize RPC manager and providers
    this.rpcManager = new RPCManager(config.chainId);
    this.provider = this.rpcManager.getHttpProvider();
    this.wallet = new ethers.Wallet(config.privateKey, this.provider);

    // Initialize components
    this.mempoolMonitor = new MempoolMonitor();
    this.sandwichStrategy = new SandwichStrategy();
    this.arbitrageStrategy = new ArbitrageStrategy();
    this.flashloanStrategy = new FlashloanStrategy(this.provider);
    this.simulator = new BundleSimulator();
    this.gasOptimizer = new GasOptimizer();

    // Initialize Flashbots and advanced gas estimation
    this.flashbotsManager = new FlashbotsBundleManager(this.provider, this.wallet);
    this.advancedGasEstimator = new AdvancedGasEstimator(this.provider);
    this.flashbotsExecutor = new FlashbotsExecutor(
      this.provider,
      this.wallet,
      this.flashbotsManager,
      this.advancedGasEstimator,
      this.gasOptimizer
    );

    // Initialize MEV-Share if enabled
    if (config.enableMevShare) {
      this.mevShareMonitor = new MEVShareEventMonitor(this.provider);
      this.mevShareFlashloanStrategy = new MEVShareFlashloanStrategy(
        this.provider,
        this.mevShareMonitor,
        this.flashbotsManager
      );
    }

    // Initialize state
    this.state = {
      isRunning: false,
      totalProfit: BigInt(0),
      successfulTrades: 0,
      failedTrades: 0,
      lastActivity: 0,
      emergencyStop: config.emergencyStop
    };

    this.riskMetrics = {
      maxDrawdown: BigInt(0),
      winRate: 0,
      averageProfit: BigInt(0),
      totalGasSpent: BigInt(0),
      profitFactor: 0
    };

    this.setupEventListeners();
  }



  /**
   * Calculate optimal scan interval based on network and strategy
   */
  private getOptimalScanInterval(): number {
    // Use configured interval if provided
    if (config.arbitrageScanIntervalMs > 0) {
      return config.arbitrageScanIntervalMs;
    }

    // Auto-calculate based on network and features
    const isMainnet = config.chainId === 1;
    const hasMevShare = config.enableMevShare;
    const hasFlashbots = config.enableFlashbots;

    // With event-driven analysis, we can scan much less frequently
    // Periodic scanning is now just a backup to catch missed opportunities

    if (hasMevShare && isMainnet) {
      return 300000; // 5 minutes (MEV-Share handles real-time)
    }

    if (hasFlashbots && isMainnet) {
      return 180000; // 3 minutes (event-driven is primary)
    }

    if (isMainnet) {
      return 120000; // 2 minutes (event-driven + backup scanning)
    }

    // Testnet - very infrequent since it's mostly for testing
    return 300000; // 5 minutes (testnet is for testing, not profit)
  }

  private setupEventListeners(): void {
    // Listen for relevant transactions from mempool
    this.mempoolMonitor.on('pendingTransaction', async (transaction: any) => {
      await this.handleRelevantTransaction(transaction);
    });

    // Mempool status events
    this.mempoolMonitor.on('started', () => logger.info('Mempool monitoring started'));
    this.mempoolMonitor.on('stopped', () => logger.info('Mempool monitoring stopped'));

    // Error handling
    this.mempoolMonitor.on('error', (error) => {
      logger.logError(error, 'MempoolMonitor');
      this.handleError(error);
    });

    // Listen for opportunities found
    this.on('opportunityFound', async (opportunity: any) => {
      await this.handleOpportunityFound(opportunity);
    });

    this.on('bundleExecuted', this.handleBundleExecuted.bind(this));
    this.on('error', this.handleError.bind(this));
  }

  async start(): Promise<void> {
    if (this.state.isRunning) {
      enhancedLogger.warning('MEV Bot is already running');
      return;
    }

    try {
      enhancedLogger.clear();
      enhancedLogger.systemStatus('🚀 Starting Advanced MEV Bot...', { liquidity: '109.601634' });

      // Initialize status dashboard
      const networkName = config.chainId === 1 ? 'Mainnet' :
                         config.chainId === 11155111 ? 'Sepolia' : 'Testnet';

      // Get current block number
      const currentBlock = await this.provider.getBlockNumber();
      statusDashboard.updateNetworkStatus(currentBlock, networkName);
      statusDashboard.updateStrategyStatus(
        config.enableFlashloanAttacks,
        config.enableMevShare,
        config.enableArbitrage
      );

      // Update dashboard with current configuration
      // Use WETH as primary if user only has ETH, otherwise use configured token
      const primaryToken = config.flashloanPrimaryToken || 'WETH'; // Changed from USDC to WETH
      const defaultTargets = primaryToken === 'WETH' ? ['USDC', 'DAI', 'USDT'] : ['WETH', 'DAI', 'USDT'];

      statusDashboard.updateConfiguration({
        primaryToken: primaryToken,
        targetTokens: Array.isArray(config.flashloanTargetTokens)
          ? config.flashloanTargetTokens
          : (config.flashloanTargetTokens ? [config.flashloanTargetTokens] : defaultTargets),
        enableAllTokenPairs: config.enableAllTokenPairs,
        flashloanDexPairs: config.flashloanDexPairs || ['CURVE', 'UNISWAP_V3'],
        buyDex: config.flashloanBuyDex || 'CURVE',
        sellDex: config.flashloanSellDex || 'UNISWAP_V3',
        enableCrossDex: config.enableCrossDexArbitrage,
        minProfitWei: config.minProfitWei?.toString() || '0',
        minArbitrageSpread: (config.minArbitrageSpread || 0).toString() + '%',
        minBackrunProfitEth: config.minBackrunProfitEth?.toString() || '0.01',
        maxPositionSizeEth: config.maxPositionSizeEth?.toString() || '10',
        maxGasCostEth: config.maxGasCostEth?.toString() || '0.02',
        slippageTolerance: ((config.slippageTolerance || 0.01) * 100).toString(),
        arbitrageScanInterval: `${this.ARBITRAGE_SCAN_INTERVAL / 1000}s`,
        flashloanScanInterval: `${this.ARBITRAGE_SCAN_INTERVAL / 1000}s`,
        chainId: config.chainId,
        dryRun: config.dryRun
      });

      // Validate configuration
      validateConfig();

      // Initialize Flashbots
      await this.initializeFlashbots();

      // Initialize MEV-Share if enabled
      if (config.enableMevShare && this.mevShareMonitor && this.mevShareFlashloanStrategy) {
        enhancedLogger.systemStatus('🔄 Initializing MEV-Share integration...');
        await this.initializeMEVShare();
      }

      // Check wallet balance
      await this.checkWalletBalance();

      // Start mempool monitoring
      enhancedLogger.systemStatus('Starting mempool monitoring...');
      await this.mempoolMonitor.start();

      // Start arbitrage scanning
      enhancedLogger.systemStatus('Starting arbitrage scanning...');
      enhancedLogger.systemStatus(`   Scan Interval: ${this.ARBITRAGE_SCAN_INTERVAL / 1000}s`);
      this.startArbitrageScanning();

      // Start block number tracking
      this.startBlockTracking();

      this.state.isRunning = true;
      this.state.lastActivity = Date.now();

      enhancedLogger.success('MEV Bot started successfully');
      enhancedLogger.systemStatus('Starting live status dashboard in 3 seconds...');

      // Start dashboard after a short delay to let initial logs show
      setTimeout(() => {
        statusDashboard.start();
      }, 3000);

      this.emit('started');
    } catch (error) {
      enhancedLogger.error('Failed to start MEV Bot', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.state.isRunning) {
      logger.warn('MEV Bot is not running');
      return;
    }

    try {
      logger.info('🛑 Stopping MEV Bot...');

      this.state.isRunning = false;

      // Stop status dashboard
      statusDashboard.stop();

      // Stop mempool monitoring
      await this.mempoolMonitor.stop();

      // Stop MEV-Share monitoring if enabled
      if (this.mevShareFlashloanStrategy) {
        await this.mevShareFlashloanStrategy.stopMonitoring();
      }

      // Stop arbitrage scanning
      if (this.arbitrageScanTimer) {
        clearInterval(this.arbitrageScanTimer);
        this.arbitrageScanTimer = null;
      }

      // Stop block tracking
      if (this.blockUpdateTimer) {
        clearInterval(this.blockUpdateTimer);
        this.blockUpdateTimer = null;
      }

      // Clear opportunities
      this.opportunities = [];

      logger.info('✅ MEV Bot stopped successfully');
      this.emit('stopped');
    } catch (error) {
      logger.logError(error as Error, 'MEVBot.stop');
    }
  }

  private async initializeFlashbots(): Promise<void> {
    try {
      enhancedLogger.systemStatus('🔧 Initializing Flashbots integration...');

      // Initialize Flashbots bundle manager
      await this.flashbotsManager.initialize();

      if (this.flashbotsManager.isAvailable()) {
        enhancedLogger.systemStatus('✅ Flashbots provider initialized successfully');

        // Get execution stats
        const stats = await this.flashbotsExecutor.getExecutionStats();
        enhancedLogger.systemStatus(`   Recommended Strategy: ${stats.recommendedStrategy}`);
        enhancedLogger.systemStatus(`   Network Congestion: ${(stats.networkCongestion * 100).toFixed(1)}%`);
      } else {
        enhancedLogger.systemStatus('⚠️  Flashbots not available (testnet or initialization failed)');
        enhancedLogger.systemStatus('   Will use regular mempool execution');
      }

    } catch (error) {
      logger.logError(error as Error, 'MEVBot.initializeFlashbots');
      enhancedLogger.systemStatus('⚠️  Flashbots initialization failed, using mempool fallback');
    }
  }

  private async initializeMEVShare(): Promise<void> {
    try {
      if (!this.mevShareMonitor || !this.mevShareFlashloanStrategy) {
        enhancedLogger.systemStatus('⚠️  MEV-Share components not initialized');
        return;
      }

      // Initialize MEV-Share monitor
      await this.mevShareMonitor.initialize();

      if (this.mevShareMonitor.getStatus().isAvailable) {
        // Start MEV-Share monitoring
        await this.mevShareFlashloanStrategy.startMonitoring();

        logger.info('MEV-Share integration initialized successfully');

        const status = this.mevShareFlashloanStrategy.getStatus();
        logger.info('MEV-Share configuration', {
          gasProtection: status.gasProtection,
          maxGasCost: status.maxGasCost
        });
      } else {
        logger.warn('MEV-Share not available (requires mainnet)');
      }

    } catch (error) {
      logger.logError(error as Error, 'MEVBot.initializeMEVShare');
      logger.error('MEV-Share initialization failed');
    }
  }

  private async checkWalletBalance(): Promise<void> {
    const balance = await this.wallet.provider!.getBalance(this.wallet.address);
    const balanceEth = Number(ethers.formatEther(balance));

    enhancedLogger.walletBalance(balanceEth, 'ETH');

    if (balanceEth < 0.1) {
      enhancedLogger.warning('Low wallet balance - consider adding more ETH');
    }

    if (balanceEth < 0.01) {
      throw new Error('Insufficient wallet balance for MEV operations');
    }
  }

  private async handlePendingTransaction(tx: Transaction): Promise<void> {
    if (!this.state.isRunning || this.state.emergencyStop) {
      return;
    }

    try {
      this.state.lastActivity = Date.now();

      // Analyze for sandwich opportunities
      if (config.enableSandwichAttacks) {
        const sandwichOpportunity = await this.sandwichStrategy.analyzeTransaction(tx);
        if (sandwichOpportunity) {
          this.emit('opportunityFound', sandwichOpportunity);
        }
      }

      // Note: Front-running would be implemented similarly to sandwich
      // but without the back-run transaction
    } catch (error) {
      logger.debug('Error analyzing transaction', {
        hash: tx.hash,
        error: (error as Error).message
      });
    }
  }

  private async handleOpportunityFound(opportunity: MEVOpportunity): Promise<void> {
    try {
      const profitEth = ethers.formatEther(opportunity.estimatedProfit);

      // Record opportunity in dashboard
      statusDashboard.recordOpportunity(false, BigInt(opportunity.estimatedProfit.toString()));

      // Only log high-confidence opportunities to avoid spam
      if (opportunity.confidence >= 70) {
        logger.info(`MEV Opportunity: ${opportunity.type.toUpperCase()}`, {
          profit: profitEth,
          confidence: opportunity.confidence
        });
      }

      // Add to opportunities list
      this.opportunities.push(opportunity);

      // Keep only recent opportunities
      if (this.opportunities.length > this.MAX_OPPORTUNITIES) {
        this.opportunities = this.opportunities.slice(-this.MAX_OPPORTUNITIES);
      }

      // Execute if confidence is high enough
      if (opportunity.confidence >= 70) {
        logger.info('High confidence detected - executing opportunity...');
        await this.executeOpportunity(opportunity);
      }
    } catch (error) {
      enhancedLogger.error('Error handling MEV opportunity', error);
    }
  }

  private async executeOpportunity(opportunity: MEVOpportunity): Promise<void> {
    try {
      let success = false;

      switch (opportunity.type) {
        case 'sandwich':
          success = await this.sandwichStrategy.executeSandwich(opportunity);
          break;
        case 'arbitrage':
          // This would need to be implemented based on the opportunity structure
          logger.info('Arbitrage execution not yet implemented for mempool opportunities');
          break;
        case 'flashloan':
          logger.info('Flashloan opportunities are handled in the scanning loop');
          break;
        default:
          logger.warn(`Unknown opportunity type: ${opportunity.type}`);
          return;
      }

      if (success) {
        this.state.successfulTrades++;
        this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + BigInt(opportunity.estimatedProfit.toString());

        // Record successful transaction in dashboard
        statusDashboard.recordSuccessfulTransaction({
          timestamp: Date.now(),
          type: opportunity.type as any,
          profit: BigInt(opportunity.estimatedProfit.toString()),
          gasUsed: BigInt(0), // Will be updated with actual gas used
          confidence: opportunity.confidence,
          details: `${opportunity.type} executed successfully`
        });

        logger.info('✅ MEV opportunity executed successfully');
      } else {
        this.state.failedTrades++;
        logger.warn('❌ MEV opportunity execution failed');
      }

      this.updateRiskMetrics();
    } catch (error) {
      this.state.failedTrades++;
      logger.logError(error as Error, 'MEVBot.executeOpportunity');
    }
  }

  private startArbitrageScanning(): void {
    if (!config.enableArbitrage) {
      return;
    }

    this.arbitrageScanTimer = setInterval(async () => {
      if (!this.state.isRunning || this.state.emergencyStop) {
        return;
      }



      try {
        // Scan for regular arbitrage opportunities
        const routes = await this.arbitrageStrategy.scanForArbitrageOpportunities();

        for (const route of routes) {
          if (route.confidence >= 80) { // Higher threshold for arbitrage
            const success = await this.arbitrageStrategy.executeArbitrage(route);

            if (success) {
              this.state.successfulTrades++;
              this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + BigInt(route.expectedProfit.toString());
            } else {
              this.state.failedTrades++;
            }
          }
        }

        // Scan for flashloan arbitrage opportunities
        if (config.enableFlashloanAttacks) {
          const flashloanRoutes = await this.flashloanStrategy.scanForFlashloanOpportunities();

          for (const flashloanRoute of flashloanRoutes) {
            if (flashloanRoute.confidence >= 75) { // Slightly lower threshold for flashloan due to complexity
              enhancedLogger.systemStatus(`🔥 High-confidence flashloan opportunity detected (${flashloanRoute.confidence}%)`);

              // Check execution conditions
              const executionFavorable = await this.flashbotsExecutor.isExecutionFavorable({
                useFlashbots: true,
                urgency: 'fast',
                maxGasCostEth: 0.02,
                slippageTolerance: 0.3
              });

              if (!executionFavorable) {
                enhancedLogger.systemStatus('⚠️  Execution conditions unfavorable, skipping');
                continue;
              }

              // Execute using enhanced Flashbots executor
              const result = await this.flashbotsExecutor.executeFlashloan(flashloanRoute, {
                useFlashbots: this.flashbotsManager.isAvailable(),
                urgency: 'fast',
                maxGasCostEth: 0.02,
                slippageTolerance: 0.3
              });

              if (result.success) {
                this.state.successfulTrades++;
                this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + BigInt(flashloanRoute.expectedProfit.toString());

                // Record detailed successful transaction
                statusDashboard.recordSuccessfulTransaction({
                  timestamp: Date.now(),
                  type: 'flashloan',
                  profit: BigInt(flashloanRoute.expectedProfit.toString()),
                  gasUsed: result.gasUsed ? BigInt(result.gasUsed.toString()) : BigInt(0),
                  txHash: result.txHash,
                  bundleHash: result.bundleHash,
                  confidence: flashloanRoute.confidence,
                  details: `${flashloanRoute.flashloanToken.symbol} → ${flashloanRoute.arbitrageRoute.tokens[1].symbol}`
                });

                logger.info('✅ Flashloan arbitrage executed successfully', {
                  profit: ethers.formatEther(flashloanRoute.expectedProfit),
                  txHash: result.txHash,
                  bundleHash: result.bundleHash
                });
              } else {
                this.state.failedTrades++;
                logger.error(`❌ Flashloan arbitrage execution failed: ${result.error}`);
              }
            }
          }
        }

        this.updateRiskMetrics();
      } catch (error) {
        logger.debug('Error in arbitrage/flashloan scanning', { error: (error as Error).message });
      }
    }, this.ARBITRAGE_SCAN_INTERVAL);

    logger.info('Arbitrage scanning started');
  }

  private startBlockTracking(): void {
    // Update block number every 15 seconds
    this.blockUpdateTimer = setInterval(async () => {
      if (!this.state.isRunning) {
        return;
      }

      try {
        const currentBlock = await this.provider.getBlockNumber();
        const networkName = config.chainId === 1 ? 'Mainnet' :
                           config.chainId === 11155111 ? 'Sepolia' : 'Testnet';
        statusDashboard.updateNetworkStatus(currentBlock, networkName);
      } catch (error) {
        logger.debug('Error updating block number', { error: (error as Error).message });
      }
    }, 15000); // Update every 15 seconds

    logger.info('Block tracking started');
  }

  /**
   * Handle relevant transaction detected by mempool monitor
   */
  private async handleRelevantTransaction(transaction: any): Promise<void> {
    try {

      // Check if this transaction creates immediate arbitrage opportunities
      if (config.enableArbitrage) {
        await this.analyzeTransactionForArbitrage(transaction);
      }

      // Check if this transaction creates flashloan opportunities
      if (config.enableFlashloanAttacks) {
        await this.analyzeTransactionForFlashloan(transaction);
      }

      // Check for sandwich opportunities
      if (config.enableSandwichAttacks) {
        await this.analyzeTransactionForSandwich(transaction);
      }

    } catch (error) {
      logger.debug('Error analyzing relevant transaction', {
        hash: transaction.hash,
        error: (error as Error).message
      });
    }
  }

  /**
   * Analyze transaction for arbitrage opportunities
   */
  private async analyzeTransactionForArbitrage(transaction: any): Promise<void> {
    try {
      // Quick arbitrage scan triggered by this transaction
      const routes = await this.arbitrageStrategy.scanForArbitrageOpportunities();

      for (const route of routes.slice(0, 3)) { // Check top 3 opportunities
        if (route.confidence >= 70) {
          statusDashboard.recordOpportunity(false, BigInt(route.expectedProfit.toString()));

          const opportunity = {
            type: 'arbitrage' as const,
            estimatedProfit: route.expectedProfit,
            confidence: route.confidence,
            gasEstimate: route.gasEstimate,
            triggerTx: transaction.hash
          };

          this.emit('opportunityFound', opportunity);
        }
      }
    } catch (error) {
      logger.debug('Error analyzing arbitrage for transaction', {
        hash: transaction.hash,
        error: (error as Error).message
      });
    }
  }

  /**
   * Analyze transaction for flashloan opportunities
   */
  private async analyzeTransactionForFlashloan(transaction: any): Promise<void> {
    try {
      // Quick flashloan scan triggered by this transaction
      const routes = await this.flashloanStrategy.scanForFlashloanOpportunities();

      for (const route of routes.slice(0, 2)) { // Check top 2 opportunities
        if (route.confidence >= 70) {
          statusDashboard.recordOpportunity(false, BigInt(route.expectedProfit.toString()));

          const opportunity = {
            type: 'flashloan' as const,
            estimatedProfit: route.expectedProfit,
            confidence: route.confidence,
            gasEstimate: route.gasEstimate,
            triggerTx: transaction.hash
          };

          this.emit('opportunityFound', opportunity);
        }
      }
    } catch (error) {
      logger.debug('Error analyzing flashloan for transaction', {
        hash: transaction.hash,
        error: (error as Error).message
      });
    }
  }

  /**
   * Analyze transaction for sandwich opportunities
   */
  private async analyzeTransactionForSandwich(transaction: any): Promise<void> {
    try {
      // Check if this transaction is sandwichable
      const isSandwichable = await this.sandwichStrategy.isTransactionSandwichable(transaction);

      if (isSandwichable) {
        const estimatedProfit = await this.sandwichStrategy.estimateSandwichProfit(transaction);

        if (estimatedProfit && BigInt(estimatedProfit.toString()) > ethers.parseEther('0.001')) {
          statusDashboard.recordOpportunity(false, BigInt(estimatedProfit.toString()));

          const opportunity = {
            type: 'sandwich' as const,
            estimatedProfit: estimatedProfit,
            confidence: 85, // Sandwich attacks typically have high confidence
            gasEstimate: ethers.parseEther('0.01'), // Estimated gas cost
            triggerTx: transaction.hash
          };

          this.emit('opportunityFound', opportunity);
        }
      }
    } catch (error) {
      logger.debug('Error analyzing sandwich for transaction', {
        hash: transaction.hash,
        error: (error as Error).message
      });
    }
  }

  private async handleBundleExecuted(bundle: Bundle): Promise<void> {
    this.executedBundles.push(bundle);
    logger.logBundle(bundle.transactions[0]?.hash || 'unknown', bundle.transactions.length);
  }

  private handleError(error: Error): void {
    logger.logError(error, 'MEVBot');

    // Record error in dashboard
    statusDashboard.recordError(error.message);



    // Implement emergency stop if critical error
    if (error.message.includes('insufficient funds') ||
        error.message.includes('nonce too low')) {
      this.emergencyStop();
    }
  }

  private updateRiskMetrics(): void {
    const totalTrades = this.state.successfulTrades + this.state.failedTrades;

    if (totalTrades > 0) {
      this.riskMetrics.winRate = (this.state.successfulTrades / totalTrades) * 100;
      this.riskMetrics.averageProfit = BigInt(this.state.totalProfit.toString()) / BigInt(totalTrades);
    }

    // Calculate profit factor (gross profit / gross loss)
    // This would need more detailed tracking of individual trade results
    this.riskMetrics.profitFactor = this.riskMetrics.winRate / 100;
  }

  emergencyStop(): void {
    logger.warn('🚨 EMERGENCY STOP ACTIVATED');
    this.state.emergencyStop = true;
    this.stop();
    this.emit('emergencyStop');
  }

  getState(): BotState {
    return { ...this.state };
  }

  getRiskMetrics(): RiskMetrics {
    return { ...this.riskMetrics };
  }

  getRecentOpportunities(count: number = 10): MEVOpportunity[] {
    return this.opportunities.slice(-count);
  }

  getStats(): {
    state: BotState;
    riskMetrics: RiskMetrics;
    mempoolStatus: any;
    gasStats: any;
    mevShareStatus?: any;
  } {
    const stats = {
      state: this.getState(),
      riskMetrics: this.getRiskMetrics(),
      mempoolStatus: this.mempoolMonitor.getStatus(),
      gasStats: this.gasOptimizer.getGasStats()
    };

    // Add MEV-Share status if available
    if (this.mevShareFlashloanStrategy) {
      (stats as any).mevShareStatus = this.mevShareFlashloanStrategy.getStatus();
    }

    return stats;
  }

  async getWalletInfo(): Promise<{
    address: string;
    balance: string;
    nonce: number;
  }> {
    const balance = await this.wallet.provider!.getBalance(this.wallet.address);
    const nonce = await this.wallet.getNonce();

    return {
      address: this.wallet.address,
      balance: ethers.formatEther(balance),
      nonce
    };
  }
}
