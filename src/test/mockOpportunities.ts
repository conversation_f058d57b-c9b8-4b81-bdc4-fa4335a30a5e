/**
 * Mock Opportunities Generator for Testing
 * Creates artificial MEV opportunities for testing the bot logic on Sepolia
 */

import { ethers } from 'ethers';
import { FlashloanRoute, ArbitrageRoute, Pool, Token } from '../types';
import { config, ADDRESSES } from '../config';
import { logger } from '../utils/logger';

export class MockOpportunityGenerator {
  private mockEnabled: boolean;
  private opportunityCount: number = 0;

  constructor() {
    this.mockEnabled = process.env.ENABLE_TEST_MODE === 'true' || 
                      process.env.MOCK_OPPORTUNITIES === 'true' ||
                      config.chainId === 11155111; // Auto-enable on Sepolia
  }

  /**
   * Generate mock flashloan opportunities for testing
   */
  generateMockFlashloanOpportunities(): FlashloanRoute[] {
    if (!this.mockEnabled) return [];

    const opportunities: FlashloanRoute[] = [];
    this.opportunityCount++;

    // Mock opportunity 1: WETH → USDC arbitrage
    const wethToken: Token = {
      address: ADDRESSES.WETH,
      symbol: 'WETH',
      decimals: 18,
      name: 'Wrapped Ether'
    };

    const usdcToken: Token = {
      address: ADDRESSES.USDC,
      symbol: 'USDC',
      decimals: 6,
      name: 'USD Coin'
    };

    // Create mock pools with realistic but profitable price differences
    const mockV2Pool: Pool = {
      address: '******************************************', // Real Sepolia pool
      token0: usdcToken,
      token1: wethToken,
      fee: 3000,
      protocol: 'uniswap-v2',
      reserves: {
        reserve0: ethers.parseUnits('2500', 6), // 2500 USDC
        reserve1: ethers.parseEther('1') // 1 WETH (price: 2500 USDC/WETH)
      }
    };

    const mockV3Pool: Pool = {
      address: '******************************************', // Real Sepolia pool
      token0: usdcToken,
      token1: wethToken,
      fee: 3000,
      protocol: 'uniswap-v3',
      liquidity: ethers.parseEther('10'), // 10 ETH equivalent liquidity
      sqrtPriceX96: this.calculateSqrtPriceX96(2450, 6, 18) // 2450 USDC/WETH (2% difference)
    };

    // Create arbitrage route
    const arbitrageRoute: ArbitrageRoute = {
      pools: [mockV2Pool, mockV3Pool],
      tokens: [wethToken, usdcToken],
      expectedProfit: ethers.parseEther('0.015'), // 0.015 ETH profit
      gasEstimate: ethers.parseUnits('300000', 'wei'), // 300k gas
      confidence: 85
    };

    // Create flashloan route
    const flashloanAmount = ethers.parseEther('0.5'); // 0.5 WETH flashloan
    const flashloanFee = (flashloanAmount * BigInt(9)) / BigInt(10000); // 0.09% fee
    const netProfit = BigInt(arbitrageRoute.expectedProfit.toString()) - flashloanFee;

    if (netProfit > 0) {
      const flashloanRoute: FlashloanRoute = {
        flashloanToken: wethToken,
        flashloanAmount,
        flashloanPremium: flashloanFee,
        arbitrageRoute,
        expectedProfit: netProfit,
        gasEstimate: arbitrageRoute.gasEstimate,
        confidence: arbitrageRoute.confidence
      };

      opportunities.push(flashloanRoute);
      
      logger.info('Generated mock flashloan opportunity', {
        profit: ethers.formatEther(flashloanRoute.expectedProfit),
        confidence: flashloanRoute.confidence,
        flashloanAmount: ethers.formatEther(flashloanAmount),
        type: 'mock'
      });
    }

    // Mock opportunity 2: USDC → WETH arbitrage (reverse direction)
    if (this.opportunityCount % 3 === 0) { // Every 3rd scan
      const reverseArbitrageRoute: ArbitrageRoute = {
        pools: [mockV3Pool, mockV2Pool],
        tokens: [usdcToken, wethToken],
        expectedProfit: ethers.parseEther('0.008'), // Smaller profit
        gasEstimate: ethers.parseUnits('280000', 'wei'),
        confidence: 75
      };

      const reverseFlashloanAmount = ethers.parseUnits('1000', 6); // 1000 USDC
      const reverseFlashloanFee = (reverseFlashloanAmount * BigInt(9)) / BigInt(10000);
      const reverseNetProfit = BigInt(reverseArbitrageRoute.expectedProfit.toString()) - ethers.parseEther('0.001'); // Convert USDC fee to ETH

      if (reverseNetProfit > 0) {
        const reverseFlashloanRoute: FlashloanRoute = {
          flashloanToken: usdcToken,
          flashloanAmount: reverseFlashloanAmount,
          flashloanPremium: reverseFlashloanFee,
          arbitrageRoute: reverseArbitrageRoute,
          expectedProfit: reverseNetProfit,
          gasEstimate: reverseArbitrageRoute.gasEstimate,
          confidence: reverseArbitrageRoute.confidence
        };

        opportunities.push(reverseFlashloanRoute);
      }
    }

    return opportunities;
  }

  /**
   * Generate mock arbitrage opportunities
   */
  generateMockArbitrageOpportunities(): ArbitrageRoute[] {
    if (!this.mockEnabled) return [];

    const opportunities: ArbitrageRoute[] = [];

    // Simple V2 vs V3 arbitrage
    const wethToken: Token = {
      address: ADDRESSES.WETH,
      symbol: 'WETH',
      decimals: 18,
      name: 'Wrapped Ether'
    };

    const usdcToken: Token = {
      address: ADDRESSES.USDC,
      symbol: 'USDC',
      decimals: 6,
      name: 'USD Coin'
    };

    const mockV2Pool: Pool = {
      address: '******************************************',
      token0: usdcToken,
      token1: wethToken,
      fee: 3000,
      protocol: 'uniswap-v2',
      reserves: {
        reserve0: ethers.parseUnits('5000', 6), // 5000 USDC
        reserve1: ethers.parseEther('2') // 2 WETH
      }
    };

    const mockV3Pool: Pool = {
      address: '******************************************',
      token0: usdcToken,
      token1: wethToken,
      fee: 3000,
      protocol: 'uniswap-v3',
      liquidity: ethers.parseEther('20'),
      sqrtPriceX96: this.calculateSqrtPriceX96(2475, 6, 18) // Slightly different price
    };

    const arbitrageRoute: ArbitrageRoute = {
      pools: [mockV2Pool, mockV3Pool],
      tokens: [wethToken, usdcToken],
      expectedProfit: ethers.parseEther('0.005'), // 0.005 ETH profit
      gasEstimate: ethers.parseUnits('200000', 'wei'),
      confidence: 70
    };

    opportunities.push(arbitrageRoute);

    return opportunities;
  }

  /**
   * Calculate sqrtPriceX96 for Uniswap V3 from price
   */
  private calculateSqrtPriceX96(price: number, decimals0: number, decimals1: number): bigint {
    // Adjust price for token decimals
    const adjustedPrice = price * Math.pow(10, decimals0 - decimals1);
    
    // Calculate sqrt(price) * 2^96
    const sqrtPrice = Math.sqrt(adjustedPrice);
    const sqrtPriceX96 = sqrtPrice * Math.pow(2, 96);
    
    return BigInt(Math.floor(sqrtPriceX96));
  }

  /**
   * Check if mock mode is enabled
   */
  isMockEnabled(): boolean {
    return this.mockEnabled;
  }

  /**
   * Enable/disable mock mode
   */
  setMockEnabled(enabled: boolean): void {
    this.mockEnabled = enabled;
    logger.info(`Mock opportunities ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get mock opportunity statistics
   */
  getMockStats(): { enabled: boolean; opportunitiesGenerated: number } {
    return {
      enabled: this.mockEnabled,
      opportunitiesGenerated: this.opportunityCount
    };
  }

  /**
   * Reset mock opportunity counter
   */
  resetStats(): void {
    this.opportunityCount = 0;
  }
}

// Export singleton instance
export const mockOpportunityGenerator = new MockOpportunityGenerator();
