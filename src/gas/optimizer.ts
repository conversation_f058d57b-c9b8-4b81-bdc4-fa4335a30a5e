import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { AdvancedGasEstimator, GasEstimate } from './advanced-estimator';
import { enhancedLogger } from '../utils/enhancedLogger';
import { GasStrategy } from '../types';

export class GasOptimizer {
  private provider: ethers.JsonRpcProvider;
  private gasHistory: Array<{ timestamp: number; baseFee: ethers.BigNumberish; priorityFee: ethers.BigNumberish }> = [];
  private readonly HISTORY_SIZE = 100;
  private advancedEstimator: AdvancedGasEstimator;

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    this.advancedEstimator = new AdvancedGasEstimator(this.provider);
  }

  async getCurrentGasStrategy(): Promise<GasStrategy> {
    try {
      // Calculating optimal gas strategy - removed verbose logging to reduce spam

      // Get advanced gas estimates from multiple sources
      const advancedEstimates = await this.advancedEstimator.getGasEstimates();

      // Get provider data as fallback
      const [feeData, block] = await Promise.all([
        this.provider.getFeeData(),
        this.provider.getBlock('latest')
      ]);

      if (!feeData.gasPrice || !block) {
        throw new Error('Failed to fetch gas data');
      }

      // Use advanced estimates if available, otherwise fallback to provider
      const baseFee = advancedEstimates.baseFee || feeData.maxFeePerGas || feeData.gasPrice;
      const priorityFee = advancedEstimates.priorityFee || feeData.maxPriorityFeePerGas || ethers.parseUnits('2', 'gwei');

      // Store in history
      this.addToHistory(baseFee, priorityFee);

      // Calculate optimized gas strategy using advanced estimates
      const strategy = this.calculateOptimalStrategyAdvanced(advancedEstimates, baseFee, priorityFee);

      // Gas strategy optimized - removed verbose logging to reduce spam

      return strategy;
    } catch (error) {
      logger.logError(error as Error, 'GasOptimizer.getCurrentGasStrategy');
      enhancedLogger.systemStatus('⚠️  Using fallback gas strategy');
      return this.getFallbackStrategy();
    }
  }

  private calculateOptimalStrategyAdvanced(estimates: GasEstimate, baseFee: ethers.BigNumberish, priorityFee: ethers.BigNumberish): GasStrategy {
    // Use fast gas price for MEV transactions (competitive but not wasteful)
    const targetGasPrice = estimates.fast;
    const targetBaseFee = estimates.baseFee || baseFee;
    const targetPriorityFee = estimates.priorityFee || priorityFee;

    // Apply MEV-specific optimizations
    const baseFeeGwei = Number(ethers.formatUnits(targetBaseFee, 'gwei'));
    const priorityFeeGwei = Number(ethers.formatUnits(targetPriorityFee, 'gwei'));

    // For MEV, we want to be competitive but not overpay
    // Use the fast estimate but cap it at our maximum
    const optimizedPriorityFee = Math.min(
      Math.round((priorityFeeGwei * 1.1) * 1000000) / 1000000, // 10% increase, rounded to 6 decimals
      config.maxPriorityFeeGwei
    );

    // Calculate max fee per gas (base fee can increase by 12.5% per block)
    const maxBaseFee = Math.min(
      Math.round((baseFeeGwei * 1.5) * 1000000) / 1000000, // Allow for base fee increases, rounded to 6 decimals
      config.maxGasPriceGwei
    );

    const maxFeePerGas = Math.round((maxBaseFee + optimizedPriorityFee) * 1000000) / 1000000;

    return {
      baseFee: ethers.parseUnits(baseFeeGwei.toFixed(6), 'gwei'),
      priorityFee: ethers.parseUnits(optimizedPriorityFee.toFixed(6), 'gwei'),
      maxFeePerGas: ethers.parseUnits(maxFeePerGas.toFixed(6), 'gwei'),
      gasLimit: ethers.parseUnits('300000', 'wei') // Default gas limit
    };
  }

  private calculateOptimalStrategy(baseFee: ethers.BigNumberish, priorityFee: ethers.BigNumberish): GasStrategy {
    const baseFeeGwei = Number(ethers.formatUnits(baseFee, 'gwei'));
    const priorityFeeGwei = Number(ethers.formatUnits(priorityFee, 'gwei'));

    // Apply MEV-specific optimizations
    let optimizedBaseFee = baseFeeGwei;
    let optimizedPriorityFee = priorityFeeGwei;

    // For MEV, we want to be competitive but not overpay
    // Increase priority fee for better inclusion probability
    optimizedPriorityFee = Math.min(
      Math.round((priorityFeeGwei * 1.2) * 1000000) / 1000000, // 20% increase, rounded to 6 decimals
      config.maxPriorityFeeGwei
    );

    // Calculate max fee per gas (base fee can increase by 12.5% per block)
    const maxBaseFee = Math.min(
      Math.round((optimizedBaseFee * 1.5) * 1000000) / 1000000, // Allow for base fee increases, rounded to 6 decimals
      config.maxGasPriceGwei
    );

    const maxFeePerGas = Math.round((maxBaseFee + optimizedPriorityFee) * 1000000) / 1000000;

    return {
      baseFee: ethers.parseUnits(optimizedBaseFee.toFixed(6), 'gwei'),
      priorityFee: ethers.parseUnits(optimizedPriorityFee.toFixed(6), 'gwei'),
      maxFeePerGas: ethers.parseUnits(maxFeePerGas.toFixed(6), 'gwei'),
      gasLimit: ethers.parseUnits('300000', 'wei') // Default gas limit
    };
  }

  async getCompetitiveGasStrategy(competitorGasPrice?: ethers.BigNumberish): Promise<GasStrategy> {
    const baseStrategy = await this.getCurrentGasStrategy();

    if (!competitorGasPrice) {
      return baseStrategy;
    }

    const competitorGwei = Number(ethers.formatUnits(competitorGasPrice, 'gwei'));
    const ourMaxFeeGwei = Number(ethers.formatUnits(baseStrategy.maxFeePerGas, 'gwei'));

    // If competitor is paying more, increase our gas price slightly
    if (competitorGwei > ourMaxFeeGwei) {
      const newMaxFee = Math.min(
        Math.round((competitorGwei * 1.01) * 1000000) / 1000000, // 1% higher than competitor, rounded
        config.maxGasPriceGwei
      );

      const newPriorityFee = Math.min(
        Math.round((newMaxFee - Number(ethers.formatUnits(baseStrategy.baseFee, 'gwei'))) * 1000000) / 1000000,
        config.maxPriorityFeeGwei
      );

      return {
        ...baseStrategy,
        priorityFee: ethers.parseUnits(newPriorityFee.toFixed(6), 'gwei'),
        maxFeePerGas: ethers.parseUnits(newMaxFee.toFixed(6), 'gwei')
      };
    }

    return baseStrategy;
  }

  async estimateGasForTransaction(to: string, data: string, value?: ethers.BigNumberish): Promise<ethers.BigNumberish> {
    try {
      const gasEstimate = await this.provider.estimateGas({
        to,
        data,
        value: value || 0
      });

      // Add 20% buffer for MEV transactions
      return (gasEstimate * BigInt(120)) / BigInt(100);
    } catch (error) {
      logger.logError(error as Error, 'GasOptimizer.estimateGasForTransaction');
      // Return a conservative estimate
      return ethers.parseUnits('300000', 'wei');
    }
  }

  async calculateBundleGasCost(transactions: Array<{ to: string; data: string; value?: ethers.BigNumberish }>): Promise<{
    totalGasLimit: ethers.BigNumberish;
    totalGasCost: ethers.BigNumberish;
    gasStrategy: GasStrategy;
  }> {
    const gasStrategy = await this.getCurrentGasStrategy();
    let totalGasLimit = BigInt(0);

    for (const tx of transactions) {
      const gasEstimate = await this.estimateGasForTransaction(tx.to, tx.data, tx.value);
      totalGasLimit += BigInt(gasEstimate.toString());
    }

    const totalGasCost = totalGasLimit * BigInt(gasStrategy.maxFeePerGas.toString());

    return {
      totalGasLimit,
      totalGasCost,
      gasStrategy
    };
  }

  isProfitable(estimatedProfit: ethers.BigNumberish, gasCost: ethers.BigNumberish): boolean {
    const profit = typeof estimatedProfit === 'string' ? ethers.parseEther(estimatedProfit) : BigInt(estimatedProfit.toString());
    const cost = typeof gasCost === 'string' ? ethers.parseEther(gasCost) : BigInt(gasCost.toString());
    const minProfit = BigInt(config.minProfitWei);

    const netProfit = profit - cost;
    return netProfit >= minProfit;
  }

  getGasPricePercentile(percentile: number): ethers.BigNumberish {
    if (this.gasHistory.length === 0) {
      return ethers.parseUnits('20', 'gwei'); // Default
    }

    const sortedBaseFees = this.gasHistory
      .map(h => Number(ethers.formatUnits(h.baseFee, 'gwei')))
      .sort((a, b) => a - b);

    const index = Math.floor((percentile / 100) * sortedBaseFees.length);
    const value = sortedBaseFees[Math.min(index, sortedBaseFees.length - 1)];

    return ethers.parseUnits(value.toString(), 'gwei');
  }

  async getOptimalGasForSpeed(targetBlocksAhead: number = 1): Promise<GasStrategy> {
    const baseStrategy = await this.getCurrentGasStrategy();

    // Increase gas price based on how quickly we want inclusion
    const speedMultiplier = Math.min(1 + (targetBlocksAhead - 1) * 0.1, 2.0); // Max 2x

    const newPriorityFee = Math.min(
      Math.round((Number(ethers.formatUnits(baseStrategy.priorityFee, 'gwei')) * speedMultiplier) * 1000000) / 1000000,
      config.maxPriorityFeeGwei
    );

    const newMaxFee = Math.min(
      Math.round((Number(ethers.formatUnits(baseStrategy.maxFeePerGas, 'gwei')) * speedMultiplier) * 1000000) / 1000000,
      config.maxGasPriceGwei
    );

    return {
      ...baseStrategy,
      priorityFee: ethers.parseUnits(newPriorityFee.toFixed(6), 'gwei'),
      maxFeePerGas: ethers.parseUnits(newMaxFee.toFixed(6), 'gwei')
    };
  }

  private addToHistory(baseFee: ethers.BigNumberish, priorityFee: ethers.BigNumberish): void {
    this.gasHistory.push({
      timestamp: Date.now(),
      baseFee: BigInt(baseFee.toString()),
      priorityFee: BigInt(priorityFee.toString())
    });

    // Keep only recent history
    if (this.gasHistory.length > this.HISTORY_SIZE) {
      this.gasHistory = this.gasHistory.slice(-this.HISTORY_SIZE);
    }
  }

  private getFallbackStrategy(): GasStrategy {
    return {
      baseFee: ethers.parseUnits('20', 'gwei'),
      priorityFee: ethers.parseUnits('2', 'gwei'),
      maxFeePerGas: ethers.parseUnits('25', 'gwei'),
      gasLimit: ethers.parseUnits('300000', 'wei')
    };
  }

  getGasStats(): {
    averageBaseFee: string;
    averagePriorityFee: string;
    minBaseFee: string;
    maxBaseFee: string;
    historySize: number;
  } {
    if (this.gasHistory.length === 0) {
      return {
        averageBaseFee: '0',
        averagePriorityFee: '0',
        minBaseFee: '0',
        maxBaseFee: '0',
        historySize: 0
      };
    }

    const baseFees = this.gasHistory.map(h => Number(ethers.formatUnits(h.baseFee, 'gwei')));
    const priorityFees = this.gasHistory.map(h => Number(ethers.formatUnits(h.priorityFee, 'gwei')));

    return {
      averageBaseFee: (baseFees.reduce((a, b) => a + b, 0) / baseFees.length).toFixed(2),
      averagePriorityFee: (priorityFees.reduce((a, b) => a + b, 0) / priorityFees.length).toFixed(2),
      minBaseFee: Math.min(...baseFees).toFixed(2),
      maxBaseFee: Math.max(...baseFees).toFixed(2),
      historySize: this.gasHistory.length
    };
  }
}
