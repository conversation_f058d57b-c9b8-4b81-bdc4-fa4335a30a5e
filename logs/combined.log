{"level":"info","message":"🤖 Initializing Advanced MEV Bot...","timestamp":"2025-06-17T20:43:55.435Z"}
{"level":"info","message":"WebSocket provider initialized","timestamp":"2025-06-17T20:43:55.483Z"}
{"level":"info","message":"Flashbots provider initialized","timestamp":"2025-06-17T20:43:55.519Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.520Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.520Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.521Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.522Z"}
{"level":"info","message":"Flashbots provider initialized for simulation","timestamp":"2025-06-17T20:43:55.522Z"}
{"level":"info","message":"Starting mempool monitor...","timestamp":"2025-06-17T20:43:55.663Z"}
{"level":"info","message":"WebSocket mempool monitoring started","timestamp":"2025-06-17T20:43:55.663Z"}
{"level":"info","message":"Flashbots mempool monitoring started","timestamp":"2025-06-17T20:43:55.664Z"}
{"level":"info","message":"Mempool monitoring started","timestamp":"2025-06-17T20:43:55.664Z"}
{"level":"info","message":"Mempool monitor started successfully","timestamp":"2025-06-17T20:43:55.664Z"}
{"level":"info","message":"Block tracking started","timestamp":"2025-06-17T20:43:55.664Z"}
{"level":"info","message":"🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.","timestamp":"2025-06-17T20:43:55.665Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.197Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.198Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.198Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.200Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.200Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.200Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.201Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.201Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.201Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.202Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.202Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.202Z"}
{"crossDex":true,"dexPairs":"UNISWAP_V2, UNISWAP_V3","level":"debug","message":"Scanning for flashloan opportunities","timestamp":"2025-06-17T20:43:56.202Z"}
{"availableDexs":["UNISWAP_V2","UNISWAP_V3","BALANCER"],"configuredDexs":["UNISWAP_V2","UNISWAP_V3"],"flashloanToken":"WETH","level":"debug","message":"Starting flashloan opportunity scan","targetTokens":["WETH","USDC"],"timestamp":"2025-06-17T20:43:56.203Z"}
{"level":"debug","message":"Flashloan scan configuration","primaryToken":"WETH (Wrapped Ether)","targetTokens":"WETH, USDC","timestamp":"2025-06-17T20:43:56.203Z"}
{"hasReserves":false,"hasTick":true,"inputToken0":"WETH","inputToken1":"USDC","level":"debug","message":"Calculating pool price","poolToken0":"USDC","poolToken1":"WETH","protocol":"uniswap-v3","tick":"155180","timestamp":"2025-06-17T20:43:56.269Z"}
